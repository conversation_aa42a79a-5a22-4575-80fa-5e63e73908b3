[{"merged": "com.intensivereading.app-merged_res-44:/anim_slide_out_left.xml.flat", "source": "com.intensivereading.app-main-46:/anim/slide_out_left.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_play.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_play.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_pause.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_pause.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_navigate_before.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_navigate_before.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/layout_item_image_page.xml.flat", "source": "com.intensivereading.app-main-46:/layout/item_image_page.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/xml_data_extraction_rules.xml.flat", "source": "com.intensivereading.app-main-46:/xml/data_extraction_rules.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_launcher_background.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_launcher_background.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_navigate_next.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_navigate_next.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.intensivereading.app-main-46:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_mic.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_mic.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.intensivereading.app-main-46:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_launcher_foreground.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/anim_slide_in_left.xml.flat", "source": "com.intensivereading.app-main-46:/anim/slide_in_left.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_arrow_back.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_arrow_back.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/xml_backup_rules.xml.flat", "source": "com.intensivereading.app-main-46:/xml/backup_rules.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/drawable_ic_calendar.xml.flat", "source": "com.intensivereading.app-main-46:/drawable/ic_calendar.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/anim_slide_out_right.xml.flat", "source": "com.intensivereading.app-main-46:/anim/slide_out_right.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/layout_activity_main.xml.flat", "source": "com.intensivereading.app-main-46:/layout/activity_main.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.intensivereading.app-main-46:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/layout_fragment_detail.xml.flat", "source": "com.intensivereading.app-main-46:/layout/fragment_detail.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/navigation_nav_graph.xml.flat", "source": "com.intensivereading.app-main-46:/navigation/nav_graph.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/anim_slide_in_right.xml.flat", "source": "com.intensivereading.app-main-46:/anim/slide_in_right.xml"}, {"merged": "com.intensivereading.app-merged_res-44:/layout_fragment_main.xml.flat", "source": "com.intensivereading.app-main-46:/layout/fragment_main.xml"}]