{"logs": [{"outputFile": "com.intensivereading.app-mergeDebugResources-42:/values-as/values-as.xml", "map": [{"source": "E:\\vscode_cache_jason\\caches\\transforms-3\\4aae75c2e3fee0aa6094e6d90f4a913f\\transformed\\navigation-ui-2.7.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "7780,7885", "endColumns": "104,116", "endOffsets": "7880,7997"}}, {"source": "E:\\vscode_cache_jason\\caches\\transforms-3\\6c6ffd2b5c140a59a107fe5eaa0e19c4\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,8083", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,8161"}}, {"source": "E:\\vscode_cache_jason\\caches\\transforms-3\\543740e42155bb89dcbd6bdda0f43b68\\transformed\\core-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8166", "endColumns": "100", "endOffsets": "8262"}}, {"source": "E:\\vscode_cache_jason\\caches\\transforms-3\\476b26d8fcc09bc62d64d9d4afb0a264\\transformed\\material-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1048,1137,1202,1261,1347,1411,1475,1538,1608,1672,1726,1831,1889,1951,2005,2077,2194,2281,2364,2504,2581,2662,2753,2807,2858,2924,2994,3071,3158,3229,3306,3375,3444,3535,3607,3696,3785,3859,3931,4017,4067,4133,4213,4297,4359,4423,4486,4586,4683,4775,4874,4932,4987", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,80", "endOffsets": "267,349,427,504,590,674,776,899,978,1043,1132,1197,1256,1342,1406,1470,1533,1603,1667,1721,1826,1884,1946,2000,2072,2189,2276,2359,2499,2576,2657,2748,2802,2853,2919,2989,3066,3153,3224,3301,3370,3439,3530,3602,3691,3780,3854,3926,4012,4062,4128,4208,4292,4354,4418,4481,4581,4678,4770,4869,4927,4982,5063"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,3472,3574,3697,3776,3841,3930,3995,4054,4140,4204,4268,4331,4401,4465,4519,4624,4682,4744,4798,4870,4987,5074,5157,5297,5374,5455,5546,5600,5651,5717,5787,5864,5951,6022,6099,6168,6237,6328,6400,6489,6578,6652,6724,6810,6860,6926,7006,7090,7152,7216,7279,7379,7476,7568,7667,7725,8002", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,80", "endOffsets": "317,3142,3220,3297,3383,3467,3569,3692,3771,3836,3925,3990,4049,4135,4199,4263,4326,4396,4460,4514,4619,4677,4739,4793,4865,4982,5069,5152,5292,5369,5450,5541,5595,5646,5712,5782,5859,5946,6017,6094,6163,6232,6323,6395,6484,6573,6647,6719,6805,6855,6921,7001,7085,7147,7211,7274,7374,7471,7563,7662,7720,7775,8078"}}]}]}