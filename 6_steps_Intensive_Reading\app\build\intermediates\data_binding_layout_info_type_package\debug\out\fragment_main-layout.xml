<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="51"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="11" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/subtitleText" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="35" endOffset="61"/></Target><Target id="@+id/contentLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="38" startOffset="4" endLine="131" endOffset="55"/></Target><Target id="@+id/daysGrid" view="GridLayout"><Expressions/><location startLine="49" startOffset="8" endLine="129" endOffset="20"/></Target><Target id="@+id/mondayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="64" startOffset="12" endLine="72" endOffset="37"/></Target><Target id="@+id/tuesdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="75" startOffset="12" endLine="83" endOffset="37"/></Target><Target id="@+id/wednesdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="86" startOffset="12" endLine="94" endOffset="37"/></Target><Target id="@+id/thursdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="97" startOffset="12" endLine="105" endOffset="37"/></Target><Target id="@+id/fridayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="108" startOffset="12" endLine="116" endOffset="37"/></Target><Target id="@+id/saturdayButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="119" startOffset="12" endLine="127" endOffset="37"/></Target></Targets></Layout>