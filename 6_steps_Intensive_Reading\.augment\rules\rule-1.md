# Augment 代码生成规则配置

## 基础开发规范

### 编程语言和版本
- **严格使用 Java 语言**，禁止使用 Kotlin
- 使用 Java 8+ 语法特性，包括：
  - Lambda 表达式：`() -> {}`
  - 方法引用：`Class::method`
  - Stream API：`list.stream().filter().map().collect()`
  - Optional 类型处理空值
- 目标 Android API Level 31+ (Android 12+)
- 不考虑低版本兼容性

### 架构模式
- 严格遵循 MVVM 架构模式
- 使用 ViewBinding 进行视图绑定
- Repository 模式处理数据层
- LiveData/Observable 进行数据绑定

## 设备适配规范

### 屏幕方向
- **强制横屏显示** (`android:screenOrientation="landscape"`)
- 所有 Activity 默认横屏布局
- 布局文件优先考虑横屏 UI 设计

### 目标设备规格
**设备1 (手持设备):**
- 物理尺寸: 720x1440 (横屏: 1440x720)
- 密度: 320dpi (xhdpi)
- 布局适配: `layout-land-xhdpi`

**设备2 (平板设备):**
- 物理尺寸: 1200x1920 (横屏: 1920x1200)  
- 密度: 280dpi (xhdpi)
- 布局适配: `layout-land-large-xhdpi`

### 布局适配策略
- 使用 ConstraintLayout 作为根布局
- 采用百分比布局和权重分配
- 响应式设计适配不同屏幕尺寸
- 使用 dp 单位确保密度无关

## 代码生成模板

### Activity 模板
```java
public class MainActivity extends AppCompatActivity {
    private ActivityMainBinding binding;
    private MainViewModel viewModel;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
        setupObservers();
    }
    
    private void setupObservers() {
        viewModel.getData().observe(this, data -> {
            Optional.ofNullable(data)
                .ifPresent(this::updateUI);
        });
    }
}
```

### ViewModel 模板
```java
public class MainViewModel extends ViewModel {
    private final MutableLiveData<DataModel> dataLiveData = new MutableLiveData<>();
    private final Repository repository;
    
    public MainViewModel() {
        this.repository = new Repository();
    }
    
    public LiveData<DataModel> getData() {
        return dataLiveData;
    }
    
    public void loadData() {
        repository.fetchData()
            .stream()
            .filter(Objects::nonNull)
            .findFirst()
            .ifPresent(dataLiveData::setValue);
    }
}
```

## 布局规范

### 横屏布局原则
- 充分利用横屏宽度空间
- 左右分栏或多列布局
- 导航栏放置在左侧或顶部
- 内容区域采用卡片式布局

### 资源目录结构
```
res/
├── layout-land/              # 横屏通用布局
├── layout-land-large/        # 横屏大屏布局
├── layout-land-xhdpi/        # 横屏高密度布局
├── values-land/              # 横屏数值资源
├── values-w1440dp/           # 宽度1440dp适配
└── values-w1920dp/           # 宽度1920dp适配
```

## 性能优化规范

### Java 8+ 特性使用
- 优先使用 Stream API 处理集合操作
- 使用 Optional 避免空指针异常
- Lambda 表达式简化回调代码
- 方法引用提高代码可读性

### ViewBinding 最佳实践
- 所有布局文件启用 ViewBinding
- 在 onDestroy() 中置空 binding 引用
- 使用 binding 替代 findViewById

### 内存管理
- 及时释放大对象引用
- 使用弱引用处理监听器
- 避免内存泄漏的常见场景

## 代码风格

### 命名规范
- 类名：PascalCase
- 方法名：camelCase  
- 常量：UPPER_SNAKE_CASE
- 资源ID：snake_case

### 注释规范
- 类和方法使用 JavaDoc 注释
- 复杂逻辑添加行内注释
- TODO 标记待完成功能

## 依赖管理

### 必需依赖
```gradle
implementation 'androidx.lifecycle:lifecycle-viewmodel:2.6.2'
implementation 'androidx.lifecycle:lifecycle-livedata:2.6.2'
implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
```

### 禁用依赖
- 任何 Kotlin 相关依赖
- 低于 Android 12 的兼容库