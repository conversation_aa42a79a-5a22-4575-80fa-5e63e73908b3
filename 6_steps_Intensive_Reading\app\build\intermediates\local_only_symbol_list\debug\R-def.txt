R_DEF: Internal format may change without notice
local
anim slide_in_left
anim slide_in_right
anim slide_out_left
anim slide_out_right
color accent_color
color background_dark
color background_light
color black
color button_disabled
color button_enabled
color button_playing
color button_recording
color primary_color
color primary_dark_color
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color white
drawable ic_arrow_back
drawable ic_calendar
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mic
drawable ic_navigate_before
drawable ic_navigate_next
drawable ic_pause
drawable ic_play
id action_main_to_detail
id backButton
id bottomControlBar
id contentLayout
id daysGrid
id detailFragment
id fridayButton
id imageView
id mainFragment
id mondayButton
id nav_graph
id nav_host_fragment
id nextButton
id pageIndicator
id placeholderLayout
id playButton
id previousButton
id recordButton
id recordingOverlay
id recordingTimeText
id saturdayButton
id subtitleText
id thursdayButton
id titleText
id topBar
id tuesdayButton
id viewPager
id wednesdayButton
layout activity_main
layout fragment_detail
layout fragment_main
layout item_image_page
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string app_name
string back
string cancel
string error_file_not_found
string error_permission_required
string error_playback_failed
string error_recording_failed
string friday
string grant_permission
string long_press_to_record
string monday
string next
string no_audio_file
string page_indicator
string pause
string permission_audio_message
string permission_audio_title
string permission_denied
string permission_storage_message
string permission_storage_title
string play
string previous
string record
string recording
string recording_saved
string recording_too_short
string saturday
string thursday
string tuesday
string wednesday
style ControlButtonStyle
style MainButtonStyle
style PlayButtonStyle
style RecordButtonStyle
style Theme.IntensiveReading
xml backup_rules
xml data_extraction_rules
