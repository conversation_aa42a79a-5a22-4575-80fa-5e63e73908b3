1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.intensivereading.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 音频录制权限 -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:6:5-71
12-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:6:22-68
13
14    <!-- 存储权限 -->
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:9:5-80
15-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:10:5-81
16-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:10:22-78
17
18    <!-- 音频焦点权限 -->
19    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
19-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:13:5-80
19-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:13:22-77
20
21    <permission
21-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.intensivereading.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.intensivereading.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:15:5-36:19
28        android:allowBackup="true"
28-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:16:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:17:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:fullBackupContent="@xml/backup_rules"
33-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:18:9-54
34        android:icon="@mipmap/ic_launcher"
34-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:19:9-43
35        android:label="@string/app_name"
35-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:20:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:21:9-54
37        android:supportsRtl="true"
37-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:22:9-35
38        android:testOnly="true"
39        android:theme="@style/Theme.IntensiveReading" >
39-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:23:9-54
40        <activity
40-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:26:9-34:20
41            android:name="com.intensivereading.app.ui.main.MainActivity"
41-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:27:13-49
42            android:exported="true"
42-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:28:13-36
43            android:theme="@style/Theme.IntensiveReading" >
43-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:29:13-58
44            <intent-filter>
44-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:30:13-33:29
45                <action android:name="android.intent.action.MAIN" />
45-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:31:17-69
45-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:31:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:32:17-77
47-->F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:32:27-74
48            </intent-filter>
49        </activity>
50
51        <provider
51-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.intensivereading.app.androidx-startup"
53-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <uses-library
66-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
67            android:name="androidx.window.extensions"
67-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
68            android:required="false" />
68-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
69        <uses-library
69-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
70            android:name="androidx.window.sidecar"
70-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
71            android:required="false" />
71-->[androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
72
73        <receiver
73-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
74            android:name="androidx.profileinstaller.ProfileInstallReceiver"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
75            android:directBootAware="false"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
76            android:enabled="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
77            android:exported="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
78            android:permission="android.permission.DUMP" >
78-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
80-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
83                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
83-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
86                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
86-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
89                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
89-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
90            </intent-filter>
91        </receiver>
92    </application>
93
94</manifest>
