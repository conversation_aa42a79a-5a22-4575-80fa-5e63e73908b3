<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.detail.DetailFragment">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topBar"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:background="@color/primary_color"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_arrow_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 页面指示器 -->
        <TextView
            android:id="@+id/pageIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1/10"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- ViewPager2 用于显示图片 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/bottomControlBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBar" />

    <!-- 底部控制栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomControlBar"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:background="@color/primary_color"
        android:elevation="4dp"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 上一个按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/previousButton"
            style="@style/ControlButtonStyle"
            android:enabled="false"
            android:text="@string/previous"
            app:icon="@drawable/ic_navigate_before"
            app:iconGravity="top"
            app:iconSize="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 播放按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/playButton"
            style="@style/PlayButtonStyle"
            android:text="@string/play"
            app:icon="@drawable/ic_play"
            app:iconGravity="top"
            app:iconSize="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/recordButton"
            app:layout_constraintStart_toEndOf="@id/previousButton"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 录音按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/recordButton"
            style="@style/RecordButtonStyle"
            android:text="@string/record"
            app:icon="@drawable/ic_mic"
            app:iconGravity="top"
            app:iconSize="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/nextButton"
            app:layout_constraintStart_toEndOf="@id/playButton"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 下一个按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/nextButton"
            style="@style/ControlButtonStyle"
            android:enabled="false"
            android:text="@string/next"
            app:icon="@drawable/ic_navigate_next"
            app:iconGravity="top"
            app:iconSize="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 录音状态覆盖层 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recordingOverlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#80000000"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bottomControlBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBar">

        <!-- 录音状态卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp">

                <!-- 录音图标 -->
                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_mic"
                    android:tint="@color/button_recording" />

                <!-- 录音状态文本 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/recording"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- 录音时长 -->
                <TextView
                    android:id="@+id/recordingTimeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
