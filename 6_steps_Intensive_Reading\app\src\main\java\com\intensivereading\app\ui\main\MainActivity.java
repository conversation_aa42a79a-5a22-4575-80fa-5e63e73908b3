package com.intensivereading.app.ui.main;

import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import com.intensivereading.app.databinding.ActivityMainBinding;

/**
 * 主界面Activity - 使用Navigation Component管理Fragment
 */
public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (binding != null) {
            binding = null;
        }
    }
}
