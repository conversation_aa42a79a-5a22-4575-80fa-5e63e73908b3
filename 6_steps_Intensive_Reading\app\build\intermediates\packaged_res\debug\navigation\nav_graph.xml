<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/mainFragment">

    <!-- 主界面Fragment -->
    <fragment
        android:id="@+id/mainFragment"
        android:name="com.intensivereading.app.ui.main.MainFragment"
        android:label="@string/app_name"
        tools:layout="@layout/fragment_main">
        
        <!-- 导航到详情页面的动作 -->
        <action
            android:id="@+id/action_main_to_detail"
            app:destination="@id/detailFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- 详情页面Fragment -->
    <fragment
        android:id="@+id/detailFragment"
        android:name="com.intensivereading.app.ui.detail.DetailFragment"
        android:label="详情页面"
        tools:layout="@layout/fragment_detail">
        
        <!-- 接收dayId参数 -->
        <argument
            android:name="dayId"
            app:argType="string" />
    </fragment>

</navigation>
