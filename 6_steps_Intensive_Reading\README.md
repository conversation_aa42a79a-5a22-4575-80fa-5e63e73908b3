# 六天精读 Android 应用

这是一个用于六天学习内容管理和音频记录的Android应用程序。

## 功能特性

### 核心功能
- **六天学习计划**: 支持周一到周六的学习内容管理
- **图片展示**: 每天包含10个页面的图片内容展示
- **音频录制**: 支持为每个页面录制音频内容
- **音频播放**: 播放已录制的音频文件
- **进度管理**: 只有完成录音后才能进入下一页

### 技术特性
- **现代架构**: 使用MVVM + Repository模式
- **导航组件**: Navigation Component实现Fragment间导航
- **音频管理**: 完整的音频录制和播放功能
- **权限管理**: 运行时权限请求和处理
- **文件管理**: 自动管理音频和图片文件

## 技术规范

### 开发环境
- **语言**: Java (严格不使用Kotlin)
- **最低API**: Android API 31
- **目标API**: Android API 34
- **构建工具**: Gradle 8.0+

### 架构组件
- **UI框架**: Material Design 3
- **导航**: Navigation Component
- **图片加载**: Glide
- **音频处理**: MediaRecorder + MediaPlayer
- **布局**: ViewPager2 + ConstraintLayout

### 权限要求
- `RECORD_AUDIO`: 录音功能
- `READ_EXTERNAL_STORAGE`: 读取存储
- `WRITE_EXTERNAL_STORAGE`: 写入存储

## 项目结构

```
app/src/main/java/com/intensivereading/app/
├── ui/
│   ├── main/           # 主界面相关
│   │   ├── MainActivity.java
│   │   └── MainFragment.java
│   └── detail/         # 详情页面相关
│       ├── DetailFragment.java
│       └── ImagePagerAdapter.java
├── audio/
│   ├── recorder/       # 录音功能
│   │   └── AudioRecorder.java
│   ├── player/         # 播放功能
│   │   └── AudioPlayer.java
│   └── manager/        # 音频管理
│       └── AudioManager.java
├── repository/         # 数据仓库
│   └── DataRepository.java
├── model/              # 数据模型
│   ├── DayData.java
│   ├── PageData.java
│   └── AudioState.java
└── utils/              # 工具类
    ├── FileUtils.java
    └── PermissionUtils.java
```

## 使用说明

### 安装要求
- Android 12 (API 31) 或更高版本
- 至少100MB存储空间
- 麦克风权限

### 基本操作

1. **选择学习日期**
   - 在主界面点击对应的日期按钮（周一到周六）
   - 进入该日期的学习内容页面

2. **浏览学习内容**
   - 使用ViewPager2左右滑动浏览图片
   - 查看顶部的页面指示器了解当前位置

3. **录制音频**
   - 长按录音按钮开始录制
   - 录制时会显示录音状态和时长
   - 松开按钮停止录制
   - 录音时长必须超过2秒才会保存

4. **播放音频**
   - 点击播放按钮播放当前页面的音频
   - 再次点击可暂停播放
   - 切换页面时会自动停止播放

5. **页面导航**
   - 使用底部的"上一个"和"下一个"按钮导航
   - 只有完成录音后"下一个"按钮才会启用
   - 点击返回按钮回到主界面

### 文件存储

应用会在内部存储中创建以下目录结构：
```
/data/data/com.intensivereading.app/files/
├── monday/
│   ├── images/         # 图片文件
│   └── audio/          # 音频文件
├── tuesday/
│   ├── images/
│   └── audio/
└── ... (其他日期)
```

### 音频文件命名规则
音频文件按以下格式命名：
`audio_[日期ID]_[页面序号]_[时间戳].aac`

例如：`audio_monday_1_20231201_143022.aac`

## 开发说明

### 构建项目
1. 使用Android Studio打开项目
2. 同步Gradle依赖
3. 连接Android设备或启动模拟器
4. 运行应用

### 添加学习内容
要添加图片内容，需要将图片文件放置在对应的目录中：
- 文件名格式：`page_[页面序号].jpg`
- 存储位置：`context.getFilesDir()/[日期ID]/images/`

### 自定义配置
可以在以下文件中修改配置：
- `DataRepository.java`: 修改页面数量和日期配置
- `AudioRecorder.java`: 修改录音参数和最短时长
- `strings.xml`: 修改界面文本

## 故障排除

### 常见问题

1. **录音功能不工作**
   - 检查是否授予了录音权限
   - 确认设备有可用的麦克风
   - 检查存储空间是否充足

2. **播放功能异常**
   - 确认音频文件存在且完整
   - 检查音频格式是否支持
   - 重启应用重新初始化音频系统

3. **页面导航问题**
   - 确认已完成当前页面的录音
   - 检查Navigation Component配置
   - 查看日志输出了解具体错误

4. **权限被拒绝**
   - 在系统设置中手动授予权限
   - 卸载重装应用重新请求权限

### 日志调试
应用使用以下TAG输出日志：
- `AudioRecorder`: 录音相关日志
- `AudioPlayer`: 播放相关日志
- `AudioManager`: 音频管理日志

使用以下命令查看日志：
```bash
adb logcat -s AudioRecorder AudioPlayer AudioManager
```

## 版本历史

### v1.0.0 (当前版本)
- 初始版本发布
- 实现基础的六天学习管理功能
- 支持音频录制和播放
- Material Design 3界面设计

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库：[GitHub链接]
- 邮箱：[联系邮箱]
