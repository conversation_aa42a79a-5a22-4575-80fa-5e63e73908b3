<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.main.MainFragment">

    <!-- 标题栏 -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/app_name"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/subtitleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="选择学习日期"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleText" />

    <!-- 主要内容区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contentLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitleText">

        <!-- 6个按钮的网格布局 -->
        <GridLayout
            android:id="@+id/daysGrid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="3"
            android:alignmentMode="alignBounds"
            android:columnOrderPreserved="false"
            android:useDefaultMargins="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- 周一按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/mondayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/monday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

            <!-- 周二按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/tuesdayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/tuesday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

            <!-- 周三按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/wednesdayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/wednesday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

            <!-- 周四按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/thursdayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/thursday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

            <!-- 周五按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/fridayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/friday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

            <!-- 周六按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/saturdayButton"
                style="@style/MainButtonStyle"
                android:layout_columnWeight="1"
                android:text="@string/saturday"
                android:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_calendar"
                app:iconGravity="top"
                app:iconSize="32dp" />

        </GridLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
