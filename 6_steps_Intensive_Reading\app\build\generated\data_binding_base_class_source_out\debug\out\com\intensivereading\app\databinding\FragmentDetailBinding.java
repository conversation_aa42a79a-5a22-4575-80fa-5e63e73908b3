// Generated by view binder compiler. Do not edit!
package com.intensivereading.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.button.MaterialButton;
import com.intensivereading.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDetailBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final ConstraintLayout bottomControlBar;

  @NonNull
  public final MaterialButton nextButton;

  @NonNull
  public final TextView pageIndicator;

  @NonNull
  public final MaterialButton playButton;

  @NonNull
  public final MaterialButton previousButton;

  @NonNull
  public final MaterialButton recordButton;

  @NonNull
  public final ConstraintLayout recordingOverlay;

  @NonNull
  public final TextView recordingTimeText;

  @NonNull
  public final ConstraintLayout topBar;

  @NonNull
  public final ViewPager2 viewPager;

  private FragmentDetailBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton backButton,
      @NonNull ConstraintLayout bottomControlBar, @NonNull MaterialButton nextButton,
      @NonNull TextView pageIndicator, @NonNull MaterialButton playButton,
      @NonNull MaterialButton previousButton, @NonNull MaterialButton recordButton,
      @NonNull ConstraintLayout recordingOverlay, @NonNull TextView recordingTimeText,
      @NonNull ConstraintLayout topBar, @NonNull ViewPager2 viewPager) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.bottomControlBar = bottomControlBar;
    this.nextButton = nextButton;
    this.pageIndicator = pageIndicator;
    this.playButton = playButton;
    this.previousButton = previousButton;
    this.recordButton = recordButton;
    this.recordingOverlay = recordingOverlay;
    this.recordingTimeText = recordingTimeText;
    this.topBar = topBar;
    this.viewPager = viewPager;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backButton;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.bottomControlBar;
      ConstraintLayout bottomControlBar = ViewBindings.findChildViewById(rootView, id);
      if (bottomControlBar == null) {
        break missingId;
      }

      id = R.id.nextButton;
      MaterialButton nextButton = ViewBindings.findChildViewById(rootView, id);
      if (nextButton == null) {
        break missingId;
      }

      id = R.id.pageIndicator;
      TextView pageIndicator = ViewBindings.findChildViewById(rootView, id);
      if (pageIndicator == null) {
        break missingId;
      }

      id = R.id.playButton;
      MaterialButton playButton = ViewBindings.findChildViewById(rootView, id);
      if (playButton == null) {
        break missingId;
      }

      id = R.id.previousButton;
      MaterialButton previousButton = ViewBindings.findChildViewById(rootView, id);
      if (previousButton == null) {
        break missingId;
      }

      id = R.id.recordButton;
      MaterialButton recordButton = ViewBindings.findChildViewById(rootView, id);
      if (recordButton == null) {
        break missingId;
      }

      id = R.id.recordingOverlay;
      ConstraintLayout recordingOverlay = ViewBindings.findChildViewById(rootView, id);
      if (recordingOverlay == null) {
        break missingId;
      }

      id = R.id.recordingTimeText;
      TextView recordingTimeText = ViewBindings.findChildViewById(rootView, id);
      if (recordingTimeText == null) {
        break missingId;
      }

      id = R.id.topBar;
      ConstraintLayout topBar = ViewBindings.findChildViewById(rootView, id);
      if (topBar == null) {
        break missingId;
      }

      id = R.id.viewPager;
      ViewPager2 viewPager = ViewBindings.findChildViewById(rootView, id);
      if (viewPager == null) {
        break missingId;
      }

      return new FragmentDetailBinding((ConstraintLayout) rootView, backButton, bottomControlBar,
          nextButton, pageIndicator, playButton, previousButton, recordButton, recordingOverlay,
          recordingTimeText, topBar, viewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
