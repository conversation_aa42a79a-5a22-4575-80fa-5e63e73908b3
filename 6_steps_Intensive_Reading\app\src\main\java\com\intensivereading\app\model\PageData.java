package com.intensivereading.app.model;

/**
 * 表示单个页面的数据
 */
public class PageData {
    private int pageNumber;         // 页面编号 (1-10)
    private String imagePath;       // 图片文件路径
    private String audioPath;       // 音频文件路径
    private boolean hasAudio;       // 是否有音频文件
    private boolean isRecorded;     // 是否已录音
    private long recordingTime;     // 录音时长（毫秒）
    
    public PageData(int pageNumber) {
        this.pageNumber = pageNumber;
        this.hasAudio = false;
        this.isRecorded = false;
        this.recordingTime = 0;
    }
    
    // Getters and Setters
    public int getPageNumber() {
        return pageNumber;
    }
    
    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    public String getImagePath() {
        return imagePath;
    }
    
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }
    
    public String getAudioPath() {
        return audioPath;
    }
    
    public void setAudioPath(String audioPath) {
        this.audioPath = audioPath;
        this.hasAudio = (audioPath != null && !audioPath.isEmpty());
    }
    
    public boolean hasAudio() {
        return hasAudio;
    }
    
    public void setHasAudio(boolean hasAudio) {
        this.hasAudio = hasAudio;
    }
    
    public boolean isRecorded() {
        return isRecorded;
    }
    
    public void setRecorded(boolean recorded) {
        this.isRecorded = recorded;
    }
    
    public long getRecordingTime() {
        return recordingTime;
    }
    
    public void setRecordingTime(long recordingTime) {
        this.recordingTime = recordingTime;
    }
    
    /**
     * 检查是否可以进入下一页
     * 只有录音完成后才能进入下一页
     */
    public boolean canProceedToNext() {
        return isRecorded && hasAudio;
    }
}
