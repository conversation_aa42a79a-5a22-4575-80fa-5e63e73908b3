package com.intensivereading.app.utils;

import android.content.Context;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 文件操作工具类
 */
public class FileUtils {
    
    private static final String IMAGES_DIR = "images";
    private static final String AUDIO_DIR = "audio";
    private static final String AUDIO_PREFIX = "audio_";
    private static final String AUDIO_EXTENSION = ".aac";
    
    /**
     * 获取图片文件路径
     */
    public static String getImagePath(Context context, String dayId, int pageNumber) {
        File dayDir = new File(context.getFilesDir(), dayId);
        File imagesDir = new File(dayDir, IMAGES_DIR);
        
        // 创建目录如果不存在
        if (!imagesDir.exists()) {
            imagesDir.mkdirs();
        }
        
        return new File(imagesDir, "page_" + pageNumber + ".jpg").getAbsolutePath();
    }
    
    /**
     * 获取音频目录
     */
    public static File getAudioDir(Context context, String dayId) {
        File dayDir = new File(context.getFilesDir(), dayId);
        File audioDir = new File(dayDir, AUDIO_DIR);
        
        // 创建目录如果不存在
        if (!audioDir.exists()) {
            audioDir.mkdirs();
        }
        
        return audioDir;
    }
    
    /**
     * 生成新的音频文件路径
     */
    public static String generateAudioPath(Context context, String dayId, int pageNumber) {
        File audioDir = getAudioDir(context, dayId);
        
        // 生成时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String timestamp = sdf.format(new Date());
        
        String fileName = AUDIO_PREFIX + dayId + "_" + pageNumber + "_" + timestamp + AUDIO_EXTENSION;
        return new File(audioDir, fileName).getAbsolutePath();
    }
    
    /**
     * 获取页面最新的音频文件路径
     */
    public static String getLatestAudioPath(Context context, String dayId, int pageNumber) {
        File audioDir = getAudioDir(context, dayId);
        
        if (!audioDir.exists()) {
            return null;
        }
        
        File[] audioFiles = audioDir.listFiles((dir, name) -> 
            name.startsWith(AUDIO_PREFIX + dayId + "_" + pageNumber + "_") && 
            name.endsWith(AUDIO_EXTENSION)
        );
        
        if (audioFiles == null || audioFiles.length == 0) {
            return null;
        }
        
        // 找到最新的文件（按修改时间）
        File latestFile = audioFiles[0];
        for (File file : audioFiles) {
            if (file.lastModified() > latestFile.lastModified()) {
                latestFile = file;
            }
        }
        
        return latestFile.getAbsolutePath();
    }
    
    /**
     * 检查文件是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    /**
     * 删除文件
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
    
    /**
     * 获取文件大小
     */
    public static long getFileSize(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return 0;
        }
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * 创建目录
     */
    public static boolean createDirectory(String dirPath) {
        if (dirPath == null || dirPath.isEmpty()) {
            return false;
        }
        File dir = new File(dirPath);
        return dir.exists() || dir.mkdirs();
    }
}
