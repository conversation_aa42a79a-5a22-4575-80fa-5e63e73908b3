package com.intensivereading.app;

import android.content.Context;

import com.intensivereading.app.model.DayData;
import com.intensivereading.app.model.PageData;
import com.intensivereading.app.repository.DataRepository;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 数据仓库单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class DataRepositoryTest {
    
    @Mock
    private Context mockContext;
    
    private DataRepository dataRepository;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 模拟Context的getFilesDir()方法
        when(mockContext.getFilesDir()).thenReturn(new java.io.File("/mock/files"));
        when(mockContext.getApplicationContext()).thenReturn(mockContext);
        
        // 注意：在实际测试中，DataRepository是单例，这里仅用于演示
        // 实际项目中可能需要使用依赖注入来更好地进行单元测试
    }
    
    @Test
    public void testGetAllDayData() {
        // 由于DataRepository是单例且依赖Context，这里主要测试逻辑
        // 在实际项目中，建议重构为可注入的依赖
        
        // 验证应该有6天的数据
        String[] expectedDayIds = {"monday", "tuesday", "wednesday", "thursday", "friday", "saturday"};
        String[] expectedDayNames = {"周一", "周二", "周三", "周四", "周五", "周六"};
        
        assertEquals("应该有6个工作日", 6, expectedDayIds.length);
        assertEquals("日期ID和名称数量应该匹配", expectedDayIds.length, expectedDayNames.length);
    }
    
    @Test
    public void testDayDataStructure() {
        // 测试DayData的基本结构
        DayData dayData = new DayData("monday", "周一");
        
        assertEquals("日期ID应该正确", "monday", dayData.getDayId());
        assertEquals("显示名称应该正确", "周一", dayData.getDisplayName());
        assertEquals("默认应该有10页", 10, dayData.getTotalPages());
        assertEquals("页面列表大小应该正确", 10, dayData.getPages().size());
        
        // 测试页面编号
        for (int i = 0; i < dayData.getPages().size(); i++) {
            PageData page = dayData.getPages().get(i);
            assertEquals("页面编号应该正确", i + 1, page.getPageNumber());
            assertFalse("初始状态应该没有音频", page.hasAudio());
            assertFalse("初始状态应该未录音", page.isRecorded());
        }
    }
    
    @Test
    public void testPageDataLogic() {
        PageData pageData = new PageData(1);
        
        // 测试初始状态
        assertEquals("页面编号应该正确", 1, pageData.getPageNumber());
        assertFalse("初始状态不能进入下一页", pageData.canProceedToNext());
        
        // 设置音频路径
        pageData.setAudioPath("/mock/audio/test.aac");
        pageData.setRecorded(true);
        
        assertTrue("设置音频后应该有音频", pageData.hasAudio());
        assertTrue("录音完成后应该可以进入下一页", pageData.canProceedToNext());
    }
    
    @Test
    public void testGetPageByNumber() {
        DayData dayData = new DayData("test", "测试");
        
        // 测试有效页面编号
        PageData page1 = dayData.getPage(1);
        assertNotNull("第1页应该存在", page1);
        assertEquals("页面编号应该正确", 1, page1.getPageNumber());
        
        PageData page10 = dayData.getPage(10);
        assertNotNull("第10页应该存在", page10);
        assertEquals("页面编号应该正确", 10, page10.getPageNumber());
        
        // 测试无效页面编号
        PageData page0 = dayData.getPage(0);
        assertNull("第0页不应该存在", page0);
        
        PageData page11 = dayData.getPage(11);
        assertNull("第11页不应该存在", page11);
    }
}
