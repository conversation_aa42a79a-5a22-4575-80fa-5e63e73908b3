<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.IntensiveReading" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
    
    <!-- 主界面按钮样式 -->
    <style name="MainButtonStyle" parent="Widget.Material3.Button">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">12dp</item>
    </style>
    
    <!-- 控制按钮样式 -->
    <style name="ControlButtonStyle" parent="Widget.Material3.Button.Icon">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cornerRadius">32dp</item>
    </style>
    
    <!-- 录音按钮样式 -->
    <style name="RecordButtonStyle" parent="ControlButtonStyle">
        <item name="android:backgroundTint">@color/button_recording</item>
    </style>
    
    <!-- 播放按钮样式 -->
    <style name="PlayButtonStyle" parent="ControlButtonStyle">
        <item name="android:backgroundTint">@color/button_playing</item>
    </style>
</resources>
