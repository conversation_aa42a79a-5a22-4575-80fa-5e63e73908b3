package com.intensivereading.app.repository;

import android.content.Context;
import com.intensivereading.app.model.DayData;
import com.intensivereading.app.model.PageData;
import com.intensivereading.app.utils.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据仓库类，管理应用的所有数据
 */
public class DataRepository {
    private static DataRepository instance;
    private Context context;
    private Map<String, DayData> dayDataMap;
    
    // 天的标识符数组
    private static final String[] DAY_IDS = {
        "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"
    };
    
    // 天的显示名称数组
    private static final String[] DAY_NAMES = {
        "周一", "周二", "周三", "周四", "周五", "周六"
    };
    
    private DataRepository(Context context) {
        this.context = context.getApplicationContext();
        this.dayDataMap = new HashMap<>();
        initializeDayData();
    }
    
    public static synchronized DataRepository getInstance(Context context) {
        if (instance == null) {
            instance = new DataRepository(context);
        }
        return instance;
    }
    
    /**
     * 初始化天数据
     */
    private void initializeDayData() {
        for (int i = 0; i < DAY_IDS.length; i++) {
            DayData dayData = new DayData(DAY_IDS[i], DAY_NAMES[i]);
            dayDataMap.put(DAY_IDS[i], dayData);
            
            // 检查并初始化每一页的文件路径
            initializePagePaths(dayData);
        }
    }
    
    /**
     * 初始化页面文件路径
     */
    private void initializePagePaths(DayData dayData) {
        String dayId = dayData.getDayId();
        
        for (PageData page : dayData.getPages()) {
            // 设置图片路径
            String imagePath = FileUtils.getImagePath(context, dayId, page.getPageNumber());
            page.setImagePath(imagePath);
            
            // 检查是否存在音频文件
            String audioPath = FileUtils.getLatestAudioPath(context, dayId, page.getPageNumber());
            if (audioPath != null) {
                page.setAudioPath(audioPath);
                page.setRecorded(true);
            }
        }
    }
    
    /**
     * 获取所有天的数据
     */
    public List<DayData> getAllDayData() {
        List<DayData> dayDataList = new ArrayList<>();
        for (String dayId : DAY_IDS) {
            dayDataList.add(dayDataMap.get(dayId));
        }
        return dayDataList;
    }
    
    /**
     * 根据天ID获取天数据
     */
    public DayData getDayData(String dayId) {
        return dayDataMap.get(dayId);
    }
    
    /**
     * 更新页面的音频路径
     */
    public void updatePageAudioPath(String dayId, int pageNumber, String audioPath) {
        DayData dayData = dayDataMap.get(dayId);
        if (dayData != null) {
            PageData page = dayData.getPage(pageNumber);
            if (page != null) {
                page.setAudioPath(audioPath);
                page.setRecorded(true);
            }
        }
    }
    
    /**
     * 检查页面是否可以进入下一页
     */
    public boolean canProceedToNextPage(String dayId, int currentPage) {
        DayData dayData = dayDataMap.get(dayId);
        if (dayData != null) {
            PageData page = dayData.getPage(currentPage);
            return page != null && page.canProceedToNext();
        }
        return false;
    }
    
    /**
     * 获取天的索引
     */
    public int getDayIndex(String dayId) {
        for (int i = 0; i < DAY_IDS.length; i++) {
            if (DAY_IDS[i].equals(dayId)) {
                return i;
            }
        }
        return -1;
    }
}
