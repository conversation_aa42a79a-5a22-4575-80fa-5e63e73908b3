package com.intensivereading.app.ui.main;

import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;

import com.intensivereading.app.R;
import com.intensivereading.app.databinding.FragmentMainBinding;
import com.intensivereading.app.repository.DataRepository;
import com.intensivereading.app.utils.PermissionUtils;

/**
 * 主界面Fragment
 */
public class MainFragment extends Fragment {
    
    private FragmentMainBinding binding;
    private DataRepository dataRepository;
    
    // 权限请求启动器
    private ActivityResultLauncher<String[]> permissionLauncher = 
        registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), 
            result -> {
                boolean allGranted = true;
                for (Boolean granted : result.values()) {
                    if (!granted) {
                        allGranted = false;
                        break;
                    }
                }
                
                if (allGranted) {
                    Toast.makeText(requireContext(), "权限已授予", Toast.LENGTH_SHORT).show();
                } else {
                    showPermissionDeniedDialog();
                }
            });
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化数据仓库
        dataRepository = DataRepository.getInstance(requireContext());
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        binding = FragmentMainBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 设置按钮点击事件
        setupButtonClickListeners();
        
        // 检查权限
        checkPermissions();
    }
    
    /**
     * 设置按钮点击事件
     */
    private void setupButtonClickListeners() {
        // 周一按钮
        binding.mondayButton.setOnClickListener(v -> navigateToDetail("monday"));
        
        // 周二按钮
        binding.tuesdayButton.setOnClickListener(v -> navigateToDetail("tuesday"));
        
        // 周三按钮
        binding.wednesdayButton.setOnClickListener(v -> navigateToDetail("wednesday"));
        
        // 周四按钮
        binding.thursdayButton.setOnClickListener(v -> navigateToDetail("thursday"));
        
        // 周五按钮
        binding.fridayButton.setOnClickListener(v -> navigateToDetail("friday"));
        
        // 周六按钮
        binding.saturdayButton.setOnClickListener(v -> navigateToDetail("saturday"));
    }
    
    /**
     * 导航到详情页面
     */
    private void navigateToDetail(String dayId) {
        Bundle args = new Bundle();
        args.putString("dayId", dayId);
        Navigation.findNavController(requireView()).navigate(R.id.action_main_to_detail, args);
    }
    
    /**
     * 检查权限
     */
    private void checkPermissions() {
        if (!PermissionUtils.hasAllPermissions(requireContext())) {
            String[] missingPermissions = PermissionUtils.getMissingPermissions(requireContext());
            if (missingPermissions.length > 0) {
                showPermissionRequestDialog(missingPermissions);
            }
        }
    }
    
    /**
     * 显示权限请求对话框
     */
    private void showPermissionRequestDialog(String[] permissions) {
        new AlertDialog.Builder(requireContext())
            .setTitle(R.string.permission_audio_title)
            .setMessage("应用需要录音和存储权限来正常工作")
            .setPositiveButton(R.string.grant_permission, (dialog, which) -> {
                permissionLauncher.launch(permissions);
            })
            .setNegativeButton(R.string.cancel, (dialog, which) -> {
                showPermissionDeniedDialog();
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * 显示权限被拒绝对话框
     */
    private void showPermissionDeniedDialog() {
        new AlertDialog.Builder(requireContext())
            .setTitle(R.string.permission_denied)
            .setMessage(R.string.error_permission_required)
            .setPositiveButton("确定", (dialog, which) -> {
                // 可以引导用户到设置页面
            })
            .show();
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
