<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_detail" modulePackage="com.intensivereading.app" filePath="app\src\main\res\layout\fragment_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_detail_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="190" endOffset="51"/></Target><Target id="@+id/topBar" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="11" startOffset="4" endLine="48" endOffset="55"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="32" endOffset="55"/></Target><Target id="@+id/pageIndicator" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="46" endOffset="55"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="51" startOffset="4" endLine="58" endOffset="58"/></Target><Target id="@+id/bottomControlBar" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="61" startOffset="4" endLine="124" endOffset="55"/></Target><Target id="@+id/previousButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="73" startOffset="8" endLine="83" endOffset="55"/></Target><Target id="@+id/playButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="86" startOffset="8" endLine="96" endOffset="55"/></Target><Target id="@+id/recordButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="99" startOffset="8" endLine="109" endOffset="55"/></Target><Target id="@+id/nextButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="112" startOffset="8" endLine="122" endOffset="55"/></Target><Target id="@+id/recordingOverlay" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="127" startOffset="4" endLine="188" endOffset="55"/></Target><Target id="@+id/recordingTimeText" view="TextView"><Expressions/><location startLine="176" startOffset="16" endLine="182" endOffset="45"/></Target></Targets></Layout>