<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="abc_config_prefDialogWidth">440dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">60%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">90%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">60%</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">90%</item>
    <item name="abc_dialog_min_width_major" type="dimen">55%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">80%</item>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.Dialog.FixedSize"/>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.Dialog.FixedSize"/>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="Base.Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="Base.Theme.MaterialComponents.Light.Dialog.FixedSize"/>
    <style name="Theme.Material3.Dark.DialogWhenLarge" parent="Base.Theme.Material3.Dark.Dialog">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Theme.Material3.Light.DialogWhenLarge" parent="Base.Theme.Material3.Light.Dialog">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
</resources>