-- Merging decision tree log ---
manifest
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\vscode_cache_jason\caches\transforms-3\f34af031264337ed6a7e8c7bb57de944\transformed\viewbinding-8.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.1] E:\vscode_cache_jason\caches\transforms-3\64affabfc16503b323cc3aa090a255a2\transformed\navigation-common-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.1] E:\vscode_cache_jason\caches\transforms-3\0fd515a636320e8cff81f0a951acd69c\transformed\navigation-runtime-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.1] E:\vscode_cache_jason\caches\transforms-3\f9d12f3ed83809cc702bc43071f8abfb\transformed\navigation-fragment-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.1] E:\vscode_cache_jason\caches\transforms-3\4aae75c2e3fee0aa6094e6d90f4a913f\transformed\navigation-ui-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] E:\vscode_cache_jason\caches\transforms-3\476b26d8fcc09bc62d64d9d4afb0a264\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\vscode_cache_jason\caches\transforms-3\b906c92b55bd91e5bf8895139b074c94\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\vscode_cache_jason\caches\transforms-3\31128bea90cde15ec9f7d5279895a74a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\vscode_cache_jason\caches\transforms-3\6c6ffd2b5c140a59a107fe5eaa0e19c4\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\vscode_cache_jason\caches\transforms-3\50ccc33fbb0bac0164c2984c4867b60b\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] E:\vscode_cache_jason\caches\transforms-3\6afe23926a05daca5dec4ac250e91ce6\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] E:\vscode_cache_jason\caches\transforms-3\c9deca54bae0c8a6c3ee17eb80eac06a\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.1] E:\vscode_cache_jason\caches\transforms-3\bf31799d4ffe0c52c3996d1e86d38553\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\vscode_cache_jason\caches\transforms-3\bb4332d7a4c5485ad072126a05e1fbcb\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] E:\vscode_cache_jason\caches\transforms-3\62476f635106786434817bebada42f58\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\vscode_cache_jason\caches\transforms-3\3616e39264c87cef44c33678eeac71ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\vscode_cache_jason\caches\transforms-3\efb1f4e56f313902f2a4eda4f51f44fa\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\vscode_cache_jason\caches\transforms-3\f3e35f94573bf9e824ef1039f0fdc6f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\vscode_cache_jason\caches\transforms-3\66a9b9bdc87c18fb11b597309d6b6cd2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\vscode_cache_jason\caches\transforms-3\3f376514ddd3cc43dabb086f9161614a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\vscode_cache_jason\caches\transforms-3\4cfdb71a7a96890303046998144402fb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\vscode_cache_jason\caches\transforms-3\c0f74249997092d4c0179cfb6a2e0d9b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\bd684e9d5b751dd1c10b448853a7d0f9\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\1c9d2182645e17962ad5523579bebbc8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\dec0c51aa406ee3e706ba1dbe47ce5b1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\vscode_cache_jason\caches\transforms-3\ead40c03947223f719f3d16cf2d3c5e9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] E:\vscode_cache_jason\caches\transforms-3\410071cbbfd8affa73dbbd4b9b3a06cc\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\vscode_cache_jason\caches\transforms-3\6d91779b39772bcef31e9d7643bf4a57\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\vscode_cache_jason\caches\transforms-3\3746cd0c27655220469c6af6dfd45f89\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f1b767744d2e861c0e950cedccc4819b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\364595c9b8f8ab5504532d3dd260e13b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4fae8752d55441915395055748b9046d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\vscode_cache_jason\caches\transforms-3\e082f19b8b1e3d5d4172402b6b11638a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\vscode_cache_jason\caches\transforms-3\2dc6d621836c2b91e46a00a264e80075\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] E:\vscode_cache_jason\caches\transforms-3\9143bbf58c0d6fcbc5d533a8d872e331\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] E:\vscode_cache_jason\caches\transforms-3\d1aae17142498c9df6420a6ef4770176\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4704c29e5a1cd8c853368745a56707b9\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\vscode_cache_jason\caches\transforms-3\95ed5f8ecdfc7c3510530d48cfecd036\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\vscode_cache_jason\caches\transforms-3\12dd9b8d7b910d5d58a5ecede1965931\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\vscode_cache_jason\caches\transforms-3\a0b2aca7a1c9b673f3c6daf1bf31ec75\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] E:\vscode_cache_jason\caches\transforms-3\42512a7caf028e9317b2be6c17950fd7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\vscode_cache_jason\caches\transforms-3\64c2e087164583eab9cad5ac57bc139d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\vscode_cache_jason\caches\transforms-3\c1500460e04ad61a45f1d85b562c1cb9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] E:\vscode_cache_jason\caches\transforms-3\9a1fbf816e76d118906ffde597b916f1\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] E:\vscode_cache_jason\caches\transforms-3\9316b1ab1972a84359d0573abd26df07\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] E:\vscode_cache_jason\caches\transforms-3\c12bd1fea7249164457b3ee88652fa4c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\vscode_cache_jason\caches\transforms-3\9e19d3888f8fa3402174ebefe8e98afd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\vscode_cache_jason\caches\transforms-3\fce85ea2d6175bbb3c6ccf18c3952881\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\15cb24600e4bc7ba185939456c2315a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] E:\vscode_cache_jason\caches\transforms-3\daca5c2f225eebf374c4a0a9cf811d2d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\vscode_cache_jason\caches\transforms-3\ef1377d2bb0f4226e34bd8e9b13140d2\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:9:5-80
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:13:5-80
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:13:22-77
application
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:15:5-36:19
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:15:5-36:19
MERGED from [com.google.android.material:material:1.9.0] E:\vscode_cache_jason\caches\transforms-3\476b26d8fcc09bc62d64d9d4afb0a264\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] E:\vscode_cache_jason\caches\transforms-3\476b26d8fcc09bc62d64d9d4afb0a264\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\vscode_cache_jason\caches\transforms-3\b906c92b55bd91e5bf8895139b074c94\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\vscode_cache_jason\caches\transforms-3\b906c92b55bd91e5bf8895139b074c94\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] E:\vscode_cache_jason\caches\transforms-3\6afe23926a05daca5dec4ac250e91ce6\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] E:\vscode_cache_jason\caches\transforms-3\6afe23926a05daca5dec4ac250e91ce6\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\vscode_cache_jason\caches\transforms-3\64c2e087164583eab9cad5ac57bc139d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\vscode_cache_jason\caches\transforms-3\64c2e087164583eab9cad5ac57bc139d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] E:\vscode_cache_jason\caches\transforms-3\9a1fbf816e76d118906ffde597b916f1\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] E:\vscode_cache_jason\caches\transforms-3\9a1fbf816e76d118906ffde597b916f1\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:24:9-29
	android:icon
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:23:9-54
	android:dataExtractionRules
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:17:9-65
activity#com.intensivereading.app.ui.main.MainActivity
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:26:9-34:20
	android:exported
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:28:13-36
	android:theme
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:29:13-58
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:27:13-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:30:13-33:29
action#android.intent.action.MAIN
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:31:17-69
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:31:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:32:17-77
	android:name
		ADDED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml:32:27-74
uses-sdk
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\vscode_cache_jason\caches\transforms-3\f34af031264337ed6a7e8c7bb57de944\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\vscode_cache_jason\caches\transforms-3\f34af031264337ed6a7e8c7bb57de944\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.1] E:\vscode_cache_jason\caches\transforms-3\64affabfc16503b323cc3aa090a255a2\transformed\navigation-common-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.1] E:\vscode_cache_jason\caches\transforms-3\64affabfc16503b323cc3aa090a255a2\transformed\navigation-common-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.1] E:\vscode_cache_jason\caches\transforms-3\0fd515a636320e8cff81f0a951acd69c\transformed\navigation-runtime-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.1] E:\vscode_cache_jason\caches\transforms-3\0fd515a636320e8cff81f0a951acd69c\transformed\navigation-runtime-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.1] E:\vscode_cache_jason\caches\transforms-3\f9d12f3ed83809cc702bc43071f8abfb\transformed\navigation-fragment-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.1] E:\vscode_cache_jason\caches\transforms-3\f9d12f3ed83809cc702bc43071f8abfb\transformed\navigation-fragment-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.1] E:\vscode_cache_jason\caches\transforms-3\4aae75c2e3fee0aa6094e6d90f4a913f\transformed\navigation-ui-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.1] E:\vscode_cache_jason\caches\transforms-3\4aae75c2e3fee0aa6094e6d90f4a913f\transformed\navigation-ui-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] E:\vscode_cache_jason\caches\transforms-3\476b26d8fcc09bc62d64d9d4afb0a264\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] E:\vscode_cache_jason\caches\transforms-3\476b26d8fcc09bc62d64d9d4afb0a264\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\vscode_cache_jason\caches\transforms-3\b906c92b55bd91e5bf8895139b074c94\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\vscode_cache_jason\caches\transforms-3\b906c92b55bd91e5bf8895139b074c94\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\vscode_cache_jason\caches\transforms-3\31128bea90cde15ec9f7d5279895a74a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\vscode_cache_jason\caches\transforms-3\31128bea90cde15ec9f7d5279895a74a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\vscode_cache_jason\caches\transforms-3\6c6ffd2b5c140a59a107fe5eaa0e19c4\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\vscode_cache_jason\caches\transforms-3\6c6ffd2b5c140a59a107fe5eaa0e19c4\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\vscode_cache_jason\caches\transforms-3\50ccc33fbb0bac0164c2984c4867b60b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\vscode_cache_jason\caches\transforms-3\50ccc33fbb0bac0164c2984c4867b60b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] E:\vscode_cache_jason\caches\transforms-3\6afe23926a05daca5dec4ac250e91ce6\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] E:\vscode_cache_jason\caches\transforms-3\6afe23926a05daca5dec4ac250e91ce6\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.6.1] E:\vscode_cache_jason\caches\transforms-3\c9deca54bae0c8a6c3ee17eb80eac06a\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] E:\vscode_cache_jason\caches\transforms-3\c9deca54bae0c8a6c3ee17eb80eac06a\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] E:\vscode_cache_jason\caches\transforms-3\bf31799d4ffe0c52c3996d1e86d38553\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] E:\vscode_cache_jason\caches\transforms-3\bf31799d4ffe0c52c3996d1e86d38553\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\vscode_cache_jason\caches\transforms-3\bb4332d7a4c5485ad072126a05e1fbcb\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\vscode_cache_jason\caches\transforms-3\bb4332d7a4c5485ad072126a05e1fbcb\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] E:\vscode_cache_jason\caches\transforms-3\62476f635106786434817bebada42f58\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] E:\vscode_cache_jason\caches\transforms-3\62476f635106786434817bebada42f58\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\vscode_cache_jason\caches\transforms-3\3616e39264c87cef44c33678eeac71ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\vscode_cache_jason\caches\transforms-3\3616e39264c87cef44c33678eeac71ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\vscode_cache_jason\caches\transforms-3\efb1f4e56f313902f2a4eda4f51f44fa\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\vscode_cache_jason\caches\transforms-3\efb1f4e56f313902f2a4eda4f51f44fa\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\vscode_cache_jason\caches\transforms-3\f3e35f94573bf9e824ef1039f0fdc6f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\vscode_cache_jason\caches\transforms-3\f3e35f94573bf9e824ef1039f0fdc6f4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\vscode_cache_jason\caches\transforms-3\66a9b9bdc87c18fb11b597309d6b6cd2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\vscode_cache_jason\caches\transforms-3\66a9b9bdc87c18fb11b597309d6b6cd2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\vscode_cache_jason\caches\transforms-3\3f376514ddd3cc43dabb086f9161614a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\vscode_cache_jason\caches\transforms-3\3f376514ddd3cc43dabb086f9161614a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\vscode_cache_jason\caches\transforms-3\4cfdb71a7a96890303046998144402fb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\vscode_cache_jason\caches\transforms-3\4cfdb71a7a96890303046998144402fb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\vscode_cache_jason\caches\transforms-3\c0f74249997092d4c0179cfb6a2e0d9b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\vscode_cache_jason\caches\transforms-3\c0f74249997092d4c0179cfb6a2e0d9b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\bd684e9d5b751dd1c10b448853a7d0f9\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\bd684e9d5b751dd1c10b448853a7d0f9\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\1c9d2182645e17962ad5523579bebbc8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\1c9d2182645e17962ad5523579bebbc8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\dec0c51aa406ee3e706ba1dbe47ce5b1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\vscode_cache_jason\caches\transforms-3\dec0c51aa406ee3e706ba1dbe47ce5b1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\vscode_cache_jason\caches\transforms-3\ead40c03947223f719f3d16cf2d3c5e9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\vscode_cache_jason\caches\transforms-3\ead40c03947223f719f3d16cf2d3c5e9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\vscode_cache_jason\caches\transforms-3\410071cbbfd8affa73dbbd4b9b3a06cc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\vscode_cache_jason\caches\transforms-3\410071cbbfd8affa73dbbd4b9b3a06cc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\vscode_cache_jason\caches\transforms-3\6d91779b39772bcef31e9d7643bf4a57\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\vscode_cache_jason\caches\transforms-3\6d91779b39772bcef31e9d7643bf4a57\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\vscode_cache_jason\caches\transforms-3\3746cd0c27655220469c6af6dfd45f89\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\vscode_cache_jason\caches\transforms-3\3746cd0c27655220469c6af6dfd45f89\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f1b767744d2e861c0e950cedccc4819b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f1b767744d2e861c0e950cedccc4819b\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\364595c9b8f8ab5504532d3dd260e13b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\364595c9b8f8ab5504532d3dd260e13b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4fae8752d55441915395055748b9046d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4fae8752d55441915395055748b9046d\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\vscode_cache_jason\caches\transforms-3\e082f19b8b1e3d5d4172402b6b11638a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\vscode_cache_jason\caches\transforms-3\e082f19b8b1e3d5d4172402b6b11638a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\vscode_cache_jason\caches\transforms-3\2dc6d621836c2b91e46a00a264e80075\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\vscode_cache_jason\caches\transforms-3\2dc6d621836c2b91e46a00a264e80075\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] E:\vscode_cache_jason\caches\transforms-3\9143bbf58c0d6fcbc5d533a8d872e331\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] E:\vscode_cache_jason\caches\transforms-3\9143bbf58c0d6fcbc5d533a8d872e331\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] E:\vscode_cache_jason\caches\transforms-3\d1aae17142498c9df6420a6ef4770176\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] E:\vscode_cache_jason\caches\transforms-3\d1aae17142498c9df6420a6ef4770176\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4704c29e5a1cd8c853368745a56707b9\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\vscode_cache_jason\caches\transforms-3\4704c29e5a1cd8c853368745a56707b9\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\vscode_cache_jason\caches\transforms-3\95ed5f8ecdfc7c3510530d48cfecd036\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\vscode_cache_jason\caches\transforms-3\95ed5f8ecdfc7c3510530d48cfecd036\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\vscode_cache_jason\caches\transforms-3\12dd9b8d7b910d5d58a5ecede1965931\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\vscode_cache_jason\caches\transforms-3\12dd9b8d7b910d5d58a5ecede1965931\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\vscode_cache_jason\caches\transforms-3\a0b2aca7a1c9b673f3c6daf1bf31ec75\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\vscode_cache_jason\caches\transforms-3\a0b2aca7a1c9b673f3c6daf1bf31ec75\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] E:\vscode_cache_jason\caches\transforms-3\42512a7caf028e9317b2be6c17950fd7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] E:\vscode_cache_jason\caches\transforms-3\42512a7caf028e9317b2be6c17950fd7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\vscode_cache_jason\caches\transforms-3\64c2e087164583eab9cad5ac57bc139d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\vscode_cache_jason\caches\transforms-3\64c2e087164583eab9cad5ac57bc139d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\vscode_cache_jason\caches\transforms-3\c1500460e04ad61a45f1d85b562c1cb9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\vscode_cache_jason\caches\transforms-3\c1500460e04ad61a45f1d85b562c1cb9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] E:\vscode_cache_jason\caches\transforms-3\9a1fbf816e76d118906ffde597b916f1\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] E:\vscode_cache_jason\caches\transforms-3\9a1fbf816e76d118906ffde597b916f1\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] E:\vscode_cache_jason\caches\transforms-3\9316b1ab1972a84359d0573abd26df07\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] E:\vscode_cache_jason\caches\transforms-3\9316b1ab1972a84359d0573abd26df07\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\vscode_cache_jason\caches\transforms-3\c12bd1fea7249164457b3ee88652fa4c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\vscode_cache_jason\caches\transforms-3\c12bd1fea7249164457b3ee88652fa4c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\vscode_cache_jason\caches\transforms-3\9e19d3888f8fa3402174ebefe8e98afd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\vscode_cache_jason\caches\transforms-3\9e19d3888f8fa3402174ebefe8e98afd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\vscode_cache_jason\caches\transforms-3\fce85ea2d6175bbb3c6ccf18c3952881\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\vscode_cache_jason\caches\transforms-3\fce85ea2d6175bbb3c6ccf18c3952881\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\15cb24600e4bc7ba185939456c2315a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\vscode_cache_jason\caches\transforms-3\15cb24600e4bc7ba185939456c2315a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\vscode_cache_jason\caches\transforms-3\daca5c2f225eebf374c4a0a9cf811d2d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\vscode_cache_jason\caches\transforms-3\daca5c2f225eebf374c4a0a9cf811d2d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\vscode_cache_jason\caches\transforms-3\ef1377d2bb0f4226e34bd8e9b13140d2\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\vscode_cache_jason\caches\transforms-3\ef1377d2bb0f4226e34bd8e9b13140d2\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\renzheng\src\6_steps_Intensive_Reading\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\vscode_cache_jason\caches\transforms-3\bf1e6394bf2e47719a6982773078c8b7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\vscode_cache_jason\caches\transforms-3\34e9d2ecb03fed35e86c5ba34096e160\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\vscode_cache_jason\caches\transforms-3\f8c318a4b3eb1baf063dd220dd4435c0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\vscode_cache_jason\caches\transforms-3\344eae2b041e0d1cdf300978bf920651\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.intensivereading.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.intensivereading.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] E:\vscode_cache_jason\caches\transforms-3\543740e42155bb89dcbd6bdda0f43b68\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\vscode_cache_jason\caches\transforms-3\e9f0941213a18ba7c3be485484fb9856\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
